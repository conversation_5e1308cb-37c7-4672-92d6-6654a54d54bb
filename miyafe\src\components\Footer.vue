<template>
  <footer class="relative bg-gradient-to-br from-gray-900 via-black to-gray-900 py-16 mt-20 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
      <div class="floating-notes-footer">
        <div v-for="n in 15" :key="n" class="note-footer" :style="{ animationDelay: `${n * 0.8}s` }">♪</div>
      </div>
    </div>

    <!-- Gradient Overlay -->
    <div class="absolute inset-0 bg-gradient-to-t from-[#609EAF]/10 via-transparent to-transparent"></div>

    <div class="relative z-10 max-w-6xl mx-auto px-6">
      <!-- Main Footer Content -->
      <div class="text-center mb-12">
        <!-- Logo Section -->
        <div class="mb-8">
          <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-[#609EAF] to-[#4a7c8a] rounded-2xl mb-4 shadow-xl">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
            </svg>
          </div>
          <h3 class="text-3xl font-bold bg-gradient-to-r from-[#609EAF] via-white to-[#609EAF] bg-clip-text text-transparent mb-2">
            Sora Music
          </h3>
         
        </div>

        <!-- Description -->
        <p class="text-gray-300 text-lg max-w-2xl mx-auto mb-10 leading-relaxed">
          Create your perfect lo-fi soundtrack with AI. Transform your imagination into
          <span class="text-[#609EAF] font-semibold">beautiful melodies</span> that capture every mood and moment.
        </p>

        <!-- Social Links -->
        <div class="flex justify-center items-center space-x-6 mb-10">
          <a
            href="https://twitter.com/miyabimusic_"
            target="_blank"
            rel="noopener noreferrer"
            class="social-link group"
          >
            <div class="social-icon bg-gradient-to-br from-blue-500 to-blue-600">
              <svg class="w-6 h-6 text-white transition-transform group-hover:scale-110" fill="currentColor" viewBox="0 0 24 24">
                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
              </svg>
            </div>
            <span class="social-label">Twitter</span>
          </a>

          <a
            href="https://discord.gg/miyabimusic"
            target="_blank"
            rel="noopener noreferrer"
            class="social-link group"
          >
            <div class="social-icon bg-gradient-to-br from-indigo-500 to-purple-600">
              <svg class="w-6 h-6 text-white transition-transform group-hover:scale-110" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
              </svg>
            </div>
            <span class="social-label">Discord</span>
          </a>
        </div>
      </div>

      <!-- Bottom Section -->
      <div class="border-t border-gray-800 pt-8">
        <div class="flex flex-col md:flex-row justify-between items-center gap-4">
          <div class="text-gray-400 text-sm">
          </div>
          <div class="flex items-center gap-6 text-sm text-gray-400">
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
// No script needed for now
</script>

<style scoped>
/* Floating Musical Notes Background */
.floating-notes-footer {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.note-footer {
  position: absolute;
  font-size: 28px;
  color: #609EAF;
  animation: float-footer 15s infinite linear;
  opacity: 0.1;
}

.note-footer:nth-child(1) { left: 5%; animation-duration: 12s; }
.note-footer:nth-child(2) { left: 15%; animation-duration: 18s; }
.note-footer:nth-child(3) { left: 25%; animation-duration: 14s; }
.note-footer:nth-child(4) { left: 35%; animation-duration: 16s; }
.note-footer:nth-child(5) { left: 45%; animation-duration: 13s; }
.note-footer:nth-child(6) { left: 55%; animation-duration: 17s; }
.note-footer:nth-child(7) { left: 65%; animation-duration: 15s; }
.note-footer:nth-child(8) { left: 75%; animation-duration: 19s; }
.note-footer:nth-child(9) { left: 85%; animation-duration: 11s; }
.note-footer:nth-child(10) { left: 95%; animation-duration: 20s; }
.note-footer:nth-child(11) { left: 10%; animation-duration: 14s; }
.note-footer:nth-child(12) { left: 30%; animation-duration: 16s; }
.note-footer:nth-child(13) { left: 50%; animation-duration: 12s; }
.note-footer:nth-child(14) { left: 70%; animation-duration: 18s; }
.note-footer:nth-child(15) { left: 90%; animation-duration: 15s; }

@keyframes float-footer {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.1;
  }
  90% {
    opacity: 0.1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Social Link Styles */
.social-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.social-link:hover {
  transform: translateY(-4px);
}

.social-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.social-link:hover .social-icon {
  box-shadow:
    0 12px 24px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.social-label {
  color: #9ca3af;
  font-size: 12px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.social-link:hover .social-label {
  color: #609EAF;
}

/* Responsive Design */
@media (max-width: 768px) {
  .social-link {
    gap: 6px;
  }

  .social-icon {
    width: 48px;
    height: 48px;
  }

  .social-icon svg {
    width: 20px;
    height: 20px;
  }

  .social-label {
    font-size: 11px;
  }
}

/* Enhanced Gradient Text */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Link Hover Effects */
a[href="#"]:hover {
  text-decoration: none;
  transform: translateY(-1px);
}

/* Border Animation */
.border-t {
  position: relative;
}

.border-t::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #609EAF, transparent);
  animation: border-expand 2s ease-in-out infinite;
}

@keyframes border-expand {
  0%, 100% {
    width: 0;
  }
  50% {
    width: 200px;
  }
}
</style>
