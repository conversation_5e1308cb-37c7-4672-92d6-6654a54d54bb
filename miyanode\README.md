# MiyaNode - Miyabi Music Backend

Node.js backend for the Miyabi Music application, providing music generation and video conversion services.

## Features

- Music generation using PIAPI
- Audio to video conversion with FFmpeg
- Logging system
- Error handling
- CORS support

## Prerequisites

- Node.js (v14 or higher)
- FFmpeg installed on your system

## Installation

1. Clone the repository:
```bash
git clone https://github.com/ProdPulse/miyanode.git
cd miyanode
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the root directory:
```env
PIAPI_KEY=your_api_key_here
FRONTEND_URL=http://localhost:5173
PORT=8000
```

4. Add your static image:
- Place your background image (anime.jpg) in the `public` directory

## Usage

Start the server:
```bash
npm start
```

The server will run on `http://localhost:8000` by default.

## API Endpoints

- `GET /` - Health check
- `GET /api/music` - Check music generation status
- `POST /api/music` - Generate music
- `POST /api/convert-to-video` - Convert audio to video

## Environment Variables

- `PIAPI_KEY` - Your PIAPI API key
- `FRONTEND_URL` - Frontend application URL
- `PORT` - Server port (default: 8000)

## License

MIT
