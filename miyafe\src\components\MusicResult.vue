<template>
  <div v-if="props.result" ref="musicResultSection" class="w-full px-4 mt-12">
    <div class="max-w-6xl mx-auto">
      <!-- Header Section with Enhanced Design -->
      <div class="mb-12 text-center relative">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5 overflow-hidden">
          <div class="floating-music-notes">
            <div v-for="n in 8" :key="n" class="music-note" :style="{ animationDelay: `${n * 0.8}s` }">♪</div>
          </div>
        </div>

        <div class="relative z-10">
          <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-[#609EAF] to-[#4a7c8a] rounded-full mb-6 shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
            </svg>
          </div>

          <h2 class="text-5xl font-bold bg-gradient-to-r from-[#609EAF] via-[#4a7c8a] to-[#609EAF] bg-clip-text text-transparent mb-6 leading-tight">
            Your Masterpiece Awaits
          </h2>
          <p class="text-gray-600 text-xl max-w-4xl mx-auto leading-relaxed">
            Your exclusive lo-fi track is ready! Immerse yourself in the perfect blend of chill vibes and creative energy.
            <span class="text-[#609EAF] font-semibold">No limits, just pure musical bliss.</span>
            Hit play and let the rhythm elevate your mood. 
          </p>
        </div>
      </div>

      <!-- Main Content with Glass Effect -->
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl overflow-hidden border border-white/20">
        <!-- Audio Player Section -->
        <div class="p-6 sm:p-10 bg-gradient-to-br from-white/90 to-gray-50/90 backdrop-blur-sm">
          <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6 mb-8">
            <div class="flex items-center gap-4">
              <div class="w-12 h-12 bg-gradient-to-br from-[#609EAF] to-[#4a7c8a] rounded-xl flex items-center justify-center shadow-lg">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
                </svg>
              </div>
              <div>
                <h3 class="text-2xl font-bold text-gray-900">Your Generated Track</h3>
                <p class="text-gray-500 text-sm">Ready to play and download</p>
              </div>
            </div>

            <div class="flex flex-wrap gap-3 w-full lg:w-auto">
              <button
                @click="downloadMusic"
                class="flex-1 lg:flex-none px-6 py-3 bg-gradient-to-r from-[#609EAF] to-[#4a7c8a] text-white rounded-2xl hover:from-[#4a7c8a] hover:to-[#609EAF] transition-all duration-300 text-sm font-semibold flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                :disabled="isDownloading"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
                </svg>
                <span v-if="!isDownloading">Download MP3</span>
                <span v-else class="flex items-center gap-2">
                  <div class="loading-spinner"></div>
                  Downloading...
                </span>
              </button>
              <button
                v-if="!props.result.video_status"
                @click="startVideoConversion"
                class="flex-1 lg:flex-none px-6 py-3 bg-gradient-to-r from-[#609EAF] to-[#4a7c8a] text-white rounded-2xl hover:from-[#4a7c8a] hover:to-[#609EAF] transition-all duration-300 text-sm font-semibold flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                </svg>
                Create Video
              </button>
            </div>
          </div>

          <!-- Enhanced Audio Player -->
          <div class="premium-player bg-gradient-to-br from-white/90 to-gray-50/90 backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-white/30 shadow-xl">
            <!-- Player Header -->
            <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6 mb-8">
              <!-- Track Info with Vinyl Animation -->
              <div class="flex items-center gap-6 w-full lg:w-auto">
                <div class="relative">
                  <!-- Vinyl Record with Progress -->
                  <div class="vinyl-player" :class="{ 'spinning': isPlaying }">
                    <div class="vinyl-record">
                      <div class="vinyl-center"></div>
                      <div class="vinyl-groove"></div>
                      <div class="vinyl-groove-2"></div>
                      <div class="vinyl-groove-3"></div>
                      <!-- Progress Ring -->
                      <svg class="progress-ring" viewBox="0 0 120 120">
                        <circle
                          cx="60"
                          cy="60"
                          r="55"
                          stroke="#e5e7eb"
                          stroke-width="4"
                          fill="none"
                        />
                        <circle
                          cx="60"
                          cy="60"
                          r="55"
                          stroke="#609EAF"
                          stroke-width="4"
                          fill="none"
                          stroke-linecap="round"
                          :stroke-dasharray="circumference"
                          :stroke-dashoffset="circumference - (currentTime / duration) * circumference"
                          class="transition-all duration-300"
                        />
                      </svg>
                    </div>
                    <!-- Play Button Overlay -->
                    <button @click="togglePlay" class="play-button-overlay">
                      <svg v-if="!isPlaying" class="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                      <svg v-else class="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                      </svg>
                    </button>
                  </div>
                </div>

                <!-- Track Details -->
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <div class="w-2 h-2 bg-[#609EAF] rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-[#609EAF] uppercase tracking-wide">Now Playing</span>
                  </div>
                  <h4 class="text-xl font-bold text-gray-900 mb-1">Your Generated Track</h4>
                  <p class="text-gray-500 text-sm">Lo-Fi • AI Generated • Exclusive</p>
                </div>
              </div>

              <!-- Volume Control -->
              <div class="volume-section flex items-center gap-4 bg-white/60 rounded-xl px-4 py-3 backdrop-blur-sm border border-white/30">
                <button @click="toggleMute" class="text-gray-600 hover:text-[#609EAF] transition-colors">
                  <svg v-if="!isMuted" class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02z"/>
                  </svg>
                  <svg v-else class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
                  </svg>
                </button>
                <div class="volume-slider-container">
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    v-model="volume"
                    class="volume-slider"
                  >
                </div>
                <span class="text-xs text-gray-500 font-medium">{{ Math.round(volume * 100) }}%</span>
              </div>
            </div>

            <!-- Progress Section -->
            <div class="space-y-4">
              <!-- Seek Bar -->
              <div class="flex items-center gap-4">
                <span class="text-sm font-medium text-gray-500 min-w-[3rem]">{{ formatTime(currentTime) }}</span>
                <div class="seek-bar-container flex-1 bg-gray-200 rounded-full h-3 cursor-pointer relative overflow-hidden" @click="seek">
                  <div class="seek-bar-bg absolute inset-0 bg-gradient-to-r from-gray-200 to-gray-300"></div>
                  <div class="seek-bar-progress h-full bg-gradient-to-r from-[#609EAF] to-[#4a7c8a] rounded-full transition-all duration-300 relative" :style="{ width: `${(currentTime / duration) * 100}%` }">
                    <div class="seek-bar-thumb absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-lg border-2 border-[#609EAF]"></div>
                  </div>
                </div>
                <span class="text-sm font-medium text-gray-500 min-w-[3rem]">{{ formatTime(duration) }}</span>
              </div>

              <!-- Enhanced Waveform -->
              <div class="waveform-container bg-white/50 rounded-xl p-4 backdrop-blur-sm border border-white/30">
                <div class="enhanced-wave" :class="{ 'playing': isPlaying }">
                  <span
                    v-for="bar in waveBarData"
                    :key="bar.index"
                    class="wave-bar"
                    :style="{
                      '--bar-index': bar.index,
                      '--random-height': bar.height,
                      '--random-delay': bar.delay,
                      '--frequency': bar.frequency
                    }"
                  ></span>
                </div>
              </div>
            </div>

            <audio
              ref="audioPlayer"
              :src="props.result.cdnUrl"
              @play="isPlaying = true"
              @pause="isPlaying = false"
              @ended="isPlaying = false"
              @timeupdate="updateTime"
              @loadedmetadata="onLoadedMetadata"
              @volumechange="onVolumeChange"
              @error="handleAudioError"
              @stalled="handleAudioError"
              @abort="handleAudioError"
              preload="auto"
            >
              Your browser does not support the audio element.
            </audio>
          </div>
        </div>

        <!-- Enhanced Video Section -->
        <div v-show="showVideoSection" class="p-6 sm:p-10 bg-gradient-to-br from-gray-50/90 to-white/90 backdrop-blur-sm border-t border-white/30">
          <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6 mb-8">
            <div class="flex items-center gap-4">
              <div class="w-12 h-12 bg-gradient-to-br from-[#609EAF] to-[#4a7c8a] rounded-xl flex items-center justify-center shadow-lg">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                </svg>
              </div>
              <div>
                <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-1">
                  <h3 class="text-xl sm:text-2xl font-bold text-gray-900">Video Version</h3>
                  <span v-if="props.result.video_status === 'processing'"
                        class="px-3 py-1 bg-gradient-to-r from-amber-100 to-orange-100 text-amber-800 rounded-full text-xs font-semibold border border-amber-200 self-start sm:self-auto">
                    <div class="flex items-center gap-2">
                      <div class="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                      Processing
                    </div>
                  </span>
                  <span v-else-if="props.result.video_status === 'completed'"
                        class="px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 rounded-full text-xs font-semibold border border-green-200 self-start sm:self-auto">
                    <div class="flex items-center gap-2">
                      <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                      Ready
                    </div>
                  </span>
                </div>
                <p class="text-gray-500 text-sm">Visual experience for your track</p>
              </div>
            </div>

            <div class="flex flex-wrap gap-3 w-full lg:w-auto">
              <button
                v-if="props.result.video_url"
                @click="shareToTwitter"
                class="flex-1 lg:flex-none px-6 py-3 bg-gradient-to-r from-black to-gray-800 text-white rounded-2xl hover:from-gray-800 hover:to-black transition-all duration-300 text-sm font-semibold flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                </svg>
                Share on X
              </button>
              <button
                v-if="props.result.video_url"
                @click="copyShareLink"
                class="flex-1 lg:flex-none px-6 py-3 bg-gradient-to-r from-gray-700 to-gray-800 text-white rounded-2xl hover:from-gray-800 hover:to-gray-900 transition-all duration-300 text-sm font-semibold flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"/>
                </svg>
                Copy Link
              </button>
            </div>
          </div>

          <!-- Enhanced Video Container -->
          <div class="aspect-video bg-gradient-to-br from-gray-900 via-black to-gray-900 rounded-2xl overflow-hidden shadow-2xl border border-gray-700">
            <!-- Video Player -->
            <video
              v-if="props.result.video_status === 'completed' && props.result.video_url"
              ref="videoPlayer"
              class="w-full h-full object-cover rounded-2xl"
              controls
              :src="props.result.video_url"
              @error="console.error('Video error:', $event)"
              @loadeddata="console.log('Video loaded successfully')"
            >
              Your browser does not support the video element.
            </video>

            <!-- Enhanced Processing State -->
            <div v-else-if="props.result.video_status === 'processing'"
                 class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900 p-4 sm:p-8 relative overflow-hidden">
              <!-- Background Animation -->
              <div class="absolute inset-0 opacity-10">
                <div class="floating-particles">
                  <div v-for="n in 20" :key="n" class="particle" :style="{ animationDelay: `${n * 0.3}s` }"></div>
                </div>
              </div>

              <div class="text-center w-full max-w-lg relative z-10 px-2">
                <!-- Enhanced Loading Animation -->
                <div class="relative mb-6 sm:mb-8">
                  <div class="video-processing-ring">
                    <div class="ring-1"></div>
                    <div class="ring-2"></div>
                    <div class="ring-3"></div>
                  </div>
                  <div class="absolute inset-0 flex items-center justify-center">
                    <svg class="w-8 h-8 sm:w-12 sm:h-12 text-[#609EAF]" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                    </svg>
                  </div>
                </div>

                <h4 class="text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4">Creating Your Video</h4>
                <p class="text-base sm:text-xl text-[#609EAF] font-medium mb-4 sm:mb-6 px-2">{{ processingMessages[currentMessageIndex] }}</p>

                <!-- Enhanced Progress Dots -->
                <div class="flex justify-center space-x-2 sm:space-x-3 mb-4 sm:mb-6">
                  <div v-for="(_, index) in 4" :key="index"
                       class="w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-[#609EAF] animate-pulse"
                       :style="{ animationDelay: `${index * 0.3}s` }">
                  </div>
                </div>

                <p class="text-gray-400 text-xs sm:text-sm px-2">This may take a few moments...</p>
              </div>
            </div>

            <!-- Enhanced Error State -->
            <div v-if="!showVideo && props.result.video_status === 'failed'"
                 class="w-full h-full flex items-center justify-center bg-gradient-to-br from-red-900/20 via-gray-900 to-black p-8">
              <div class="text-center max-w-md">
                <div class="w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg class="w-10 h-10 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h4 class="text-xl font-bold text-white mb-3">Video Processing Failed</h4>
                <p class="text-gray-400 mb-6 leading-relaxed">We encountered an issue while creating your video. Don't worry, your audio is still perfect!</p>
                <button @click="retryVideoConversion"
                        class="px-6 py-3 bg-gradient-to-r from-[#609EAF] to-[#4a7c8a] text-white rounded-2xl hover:from-[#4a7c8a] hover:to-[#609EAF] transition-all duration-300 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted, computed } from 'vue'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'

const props = defineProps({
  result: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['show-toast'])

// Computed property for progress ring circumference
const circumference = computed(() => 2 * Math.PI * 55)

// Generate consistent random values for wave bars
const waveBarData = computed(() => {
  const bars = []
  for (let i = 1; i <= 40; i++) {
    bars.push({
      index: i,
      height: Math.random() * 0.7 + 0.3, // 0.3 to 1.0
      delay: Math.random() * 1.5, // 0 to 1.5s
      frequency: Math.random() * 0.8 + 0.6 // 0.6 to 1.4 (for animation speed)
    })
  }
  return bars
})

const audioPlayer = ref(null)
const videoPlayer = ref(null)
const musicResultSection = ref(null)
const isPlaying = ref(false)
const isMuted = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(1)
const pollingInterval = ref(null)
const taskId = ref(null)
const showVideo = ref(false)
const showVideoSection = ref(false)
const isDownloading = ref(false)
const processingMessages = [
  "Crafting visual poetry for your beats... ✨",
  "Painting soundwaves into motion... 🎨",
  "Weaving colors through your melody... 🌈",
  "Synchronizing rhythm with visuals... 🎵",
  "Adding cinematic magic... 🎬",
  "Polishing your visual masterpiece... 💫",
  "Almost ready to mesmerize... 🌟",
  "Final touches of brilliance... ✨"
]
const currentMessageIndex = ref(0)
const messageInterval = ref(null)
const audioLoadRetries = ref(0)
const MAX_RETRIES = 3
const RETRY_DELAY = 2000 // 2 seconds

const handleAudioError = async (event) => {
  console.error('Audio loading error:', event)
  
  if (audioLoadRetries.value < MAX_RETRIES) {
    audioLoadRetries.value++
    console.log(`Retrying audio load (${audioLoadRetries.value}/${MAX_RETRIES})...`)
    
    // Wait for a moment before retrying
    await new Promise(resolve => setTimeout(resolve, RETRY_DELAY))
    
    // Force reload the audio
    if (audioPlayer.value) {
      const currentSrc = audioPlayer.value.src
      audioPlayer.value.src = ''
      await nextTick()
      audioPlayer.value.src = currentSrc
      audioPlayer.value.load()
    }
  } else {
    console.error('Max retries reached, audio loading failed')
    emit('show-toast', {
      message: 'Audio loading failed. Please refresh the page.',
      type: 'error'
    })
  }
}

// Initialize players
onMounted(async () => {
  if (props.result) {
    await nextTick()
    if (audioPlayer.value) {
      audioPlayer.value.volume = volume.value
      // Preload the audio
      audioPlayer.value.preload = 'auto'
      // Force load the audio
      audioPlayer.value.load()
    }
    if (videoPlayer.value) {
      videoPlayer.value.volume = volume.value
    }
  }
})

// Start video conversion
const startVideoConversion = async () => {
  try {
    console.log('Starting video conversion with URL:', props.result.cdnUrl)
    showVideoSection.value = true
    currentMessageIndex.value = 0 // Reset message index
    startMessageRotation() // Start rotating messages
    
    const response = await fetch(`${API_BASE_URL}/api/video/convert`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        audioUrl: props.result.cdnUrl,
      }),
    })

    if (!response.ok) {
      throw new Error('Failed to start video conversion')
    }

    const data = await response.json()
    console.log('Video conversion response:', data)
    
    if (data.success && data.taskId) {
      console.log('Setting taskId:', data.taskId)
      taskId.value = data.taskId
      props.result.video_status = 'processing'
      props.result.video_message = processingMessages[0]
      startPolling()
    } else {
      throw new Error(data.message || 'Failed to start video conversion')
    }
  } catch (error) {
    console.error('Error starting video conversion:', error)
    props.result.video_status = 'failed'
    props.result.video_message = error.message || 'Failed to start video conversion'
    stopMessageRotation()
  }
}

const startPolling = () => {
  if (pollingInterval.value) return
  if (!taskId.value) {
    console.error('No taskId available for polling')
    return
  }

  console.log('Starting polling with taskId:', taskId.value)
  pollingInterval.value = setInterval(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/video/status/${taskId.value}`)
      if (!response.ok) {
        throw new Error('Failed to get video status')
      }

      const data = await response.json()
      console.log('Polling response:', data)
      
      if (data.success) {
        props.result.video_status = data.status
        props.result.video_message = processingMessages[currentMessageIndex.value]
        
        if (data.videoUrl) {
          props.result.video_url = data.videoUrl
        }

        if (data.status === 'completed' || data.status === 'failed') {
          stopPolling()
          stopMessageRotation()
        }
      } else {
        throw new Error(data.message || 'Failed to get video status')
      }
    } catch (error) {
      console.error('Error polling video status:', error)
      props.result.video_status = 'failed'
      props.result.video_message = error.message || 'Failed to check video status'
      stopPolling()
      stopMessageRotation()
    }
  }, 2000)
}

const stopPolling = () => {
  console.log('Stopping polling')
  if (pollingInterval.value) {
    clearInterval(pollingInterval.value)
    pollingInterval.value = null
  }
}

const retryVideoConversion = () => {
  console.log('Retrying video conversion')
  taskId.value = null
  props.result.video_status = null
  props.result.video_url = null
  props.result.video_message = null
  props.result.video_progress = 0
  showVideo.value = false
  startVideoConversion()
}

// Toggle play
function togglePlay() {
  if (!audioPlayer.value) return
  if (isPlaying.value) {
    audioPlayer.value.pause()
  } else {
    audioPlayer.value.play()
  }
}

// Toggle mute
function toggleMute() {
  if (!audioPlayer.value) return
  audioPlayer.value.muted = !audioPlayer.value.muted
  isMuted.value = audioPlayer.value.muted
}

// Update time
function updateTime() {
  if (!audioPlayer.value) return
  currentTime.value = audioPlayer.value.currentTime
}

// On loaded metadata
function onLoadedMetadata() {
  if (!audioPlayer.value) return
  duration.value = audioPlayer.value.duration
}

// On volume change
function onVolumeChange() {
  if (!audioPlayer.value) return
  volume.value = audioPlayer.value.volume
  isMuted.value = audioPlayer.value.muted
}

// Seek
function seek(event) {
  if (!audioPlayer.value) return
  const rect = event.target.getBoundingClientRect()
  const x = event.clientX - rect.left
  const percentage = x / rect.width
  audioPlayer.value.currentTime = percentage * duration.value
}

// Format time
function formatTime(seconds) {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// Download music
async function downloadMusic() {
  if (!props.result?.cdnUrl) return
  
  try {
    isDownloading.value = true
    
    // Transform URL if needed
    const cdnUrl = props.result.cdnUrl.replace(
      'sgp1.digitaloceanspaces.com/miya',
      'cdn.aimiyabi.xyz'
    )
    
    // Create a hidden iframe for download
    const iframe = document.createElement('iframe')
    iframe.style.display = 'none'
    document.body.appendChild(iframe)
    
    // Navigate iframe to download URL
    iframe.src = `${API_BASE_URL}/api/download-audio?url=${encodeURIComponent(cdnUrl)}`
    
    // Remove iframe after a delay
    setTimeout(() => {
      document.body.removeChild(iframe)
    }, 5000)
  } catch (error) {
    console.error('Download error:', error)
    emit('show-toast', {
      message: 'Failed to download music. Please try again.',
      type: 'error'
    })
  } finally {
    isDownloading.value = false
  }
}

// Share to Twitter
function shareToTwitter() {
  if (!props.result?.video_url) return;
  
  const text = "Check out this Lo-Fi track I created with MIYABI! 🎵✨ #MiyabiMusic #LoFi";
  // Use CDN URL instead of direct Digital Ocean Spaces URL
  const url = props.result.video_url.replace(
    'sgp1.digitaloceanspaces.com/miya',
    'cdn.aimiyabi.xyz'
  );
  const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
  
  window.open(twitterUrl, '_blank', 'noopener,noreferrer');
}

// Copy share link
function copyShareLink() {
  if (!props.result?.video_url) return;
  
  const url = props.result.video_url.replace(
    'sgp1.digitaloceanspaces.com/miya',
    'cdn.aimiyabi.xyz'
  );
  
  navigator.clipboard.writeText(url).then(() => {
    emit('show-toast', {
      message: 'Link copied to clipboard!',
      type: 'success'
    });
  }).catch(() => {
    emit('show-toast', {
      message: 'Failed to copy link',
      type: 'error'
    });
  });
}

// Start rotating messages when processing
const startMessageRotation = () => {
  if (messageInterval.value) return
  
  messageInterval.value = setInterval(() => {
    currentMessageIndex.value = (currentMessageIndex.value + 1) % processingMessages.length
  }, 3000) // Change message every 3 seconds
}

// Stop message rotation
const stopMessageRotation = () => {
  if (messageInterval.value) {
    clearInterval(messageInterval.value)
    messageInterval.value = null
  }
}

// Watch volume
watch(volume, (newValue) => {
  if (!audioPlayer.value) return
  audioPlayer.value.volume = newValue
})

// Watch for video URL changes
watch(() => props.result.video_url, (newUrl) => {
  if (newUrl) {
    console.log('Video URL updated:', newUrl)
    showVideoSection.value = true
    nextTick(() => {
      showVideo.value = true
    })
  }
}, { immediate: true })

// Watch for video status changes
watch(() => props.result.video_status, (newStatus, oldStatus) => {
  console.log('Video status changed:', newStatus, 'from:', oldStatus)
  if (newStatus === 'processing') {
    showVideoSection.value = true
    showVideo.value = false
  } else if (newStatus === 'completed') {
    showVideoSection.value = true
    nextTick(() => {
      showVideo.value = true
    })
  } else if (newStatus === 'failed') {
    showVideoSection.value = true
    showVideo.value = false
  } else if (!newStatus && oldStatus) {
    // Reset state when status is cleared
    showVideoSection.value = false
    showVideo.value = false
  }
}, { immediate: true })

// Watch result
watch(() => props.result, (newResult) => {
  if (newResult) {
    nextTick(() => {
      musicResultSection.value?.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      })
    })
  }
})

// On unmounted
onUnmounted(() => {
  stopPolling()
  stopMessageRotation()
})
</script>

<style scoped>
/* Floating Background Elements */
.floating-music-notes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.music-note {
  position: absolute;
  font-size: 32px;
  color: #609EAF;
  animation: float-up 12s infinite linear;
  opacity: 0.1;
}

.music-note:nth-child(1) { left: 10%; animation-duration: 10s; }
.music-note:nth-child(2) { left: 25%; animation-duration: 14s; }
.music-note:nth-child(3) { left: 40%; animation-duration: 11s; }
.music-note:nth-child(4) { left: 55%; animation-duration: 13s; }
.music-note:nth-child(5) { left: 70%; animation-duration: 9s; }
.music-note:nth-child(6) { left: 85%; animation-duration: 12s; }
.music-note:nth-child(7) { left: 15%; animation-duration: 15s; }
.music-note:nth-child(8) { left: 75%; animation-duration: 8s; }

@keyframes float-up {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.1;
  }
  90% {
    opacity: 0.1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Vinyl Player Styles */
.vinyl-player {
  position: relative;
  width: 100px;
  height: 100px;
}

.vinyl-record {
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  border-radius: 50%;
  position: relative;
  transition: transform 0.3s ease;
  box-shadow:
    0 0 0 3px #333,
    0 8px 16px rgba(0,0,0,0.3),
    inset 0 0 0 1px rgba(255,255,255,0.1);
}

.vinyl-player.spinning .vinyl-record {
  animation: vinyl-spin 3s linear infinite;
}

.vinyl-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #609EAF, #4a7c8a);
  border-radius: 50%;
  box-shadow:
    inset 0 0 0 2px #4a7c8a,
    0 2px 4px rgba(0,0,0,0.3);
}

.vinyl-groove, .vinyl-groove-2, .vinyl-groove-3 {
  position: absolute;
  border: 1px solid rgba(96, 158, 175, 0.4);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.vinyl-groove {
  width: 75px;
  height: 75px;
}

.vinyl-groove-2 {
  width: 55px;
  height: 55px;
}

.vinyl-groove-3 {
  width: 35px;
  height: 35px;
}

.progress-ring {
  position: absolute;
  top: -10px;
  left: -10px;
  width: 120px;
  height: 120px;
  transform: rotate(-90deg);
}

.play-button-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #609EAF;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  z-index: 10;
}

.play-button-overlay:hover {
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 1);
}

@keyframes vinyl-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Volume Control Styles */
.volume-slider {
  width: 80px;
  height: 6px;
  appearance: none;
  -webkit-appearance: none;
  background: linear-gradient(to right, #609EAF 0%, #e5e7eb 0%);
  border-radius: 3px;
  outline: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(135deg, #609EAF, #4a7c8a);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(96, 158, 175, 0.4);
}

/* Enhanced Waveform */
.enhanced-wave {
  height: 80px;
  display: flex;
  align-items: end;
  gap: 3px;
  padding: 12px 0;
  justify-content: center;
  background: linear-gradient(135deg, rgba(96, 158, 175, 0.05), rgba(74, 124, 138, 0.05));
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.wave-bar {
  width: 5px;
  min-height: 8px;
  height: calc(var(--random-height) * 25%);
  background: linear-gradient(to top, #e5e7eb, #d1d5db);
  border-radius: 3px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.wave-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, #609EAF, #4a7c8a, #7bb3c4);
  border-radius: 3px;
  transform: scaleY(0);
  transform-origin: bottom;
  transition: transform 0.3s ease;
}

.enhanced-wave.playing .wave-bar::before {
  animation: realistic-wave-animation calc(0.6s + var(--random-delay) * 0.4s) ease-in-out infinite;
  animation-delay: calc(var(--bar-index) * 0.04s + var(--random-delay) * 0.3s);
  animation-timing-function: cubic-bezier(0.4, 0, 0.6, 1);
}

.enhanced-wave.playing .wave-bar {
  animation: bar-glow calc(1.0s + var(--random-delay) * 0.5s) ease-in-out infinite;
  animation-delay: calc(var(--bar-index) * 0.02s + var(--random-delay) * 0.1s);
}

@keyframes realistic-wave-animation {
  0% {
    transform: scaleY(calc(0.1 + var(--random-height) * 0.1));
  }
  12% {
    transform: scaleY(calc(0.4 + var(--random-height) * 0.3));
  }
  25% {
    transform: scaleY(calc(0.7 + var(--random-height) * 0.2));
  }
  37% {
    transform: scaleY(calc(0.5 + var(--random-height) * 0.4));
  }
  50% {
    transform: scaleY(calc(0.9 + var(--random-height) * 0.1));
  }
  62% {
    transform: scaleY(calc(0.6 + var(--random-height) * 0.3));
  }
  75% {
    transform: scaleY(calc(0.8 + var(--random-height) * 0.2));
  }
  87% {
    transform: scaleY(calc(0.3 + var(--random-height) * 0.4));
  }
  100% {
    transform: scaleY(calc(0.1 + var(--random-height) * 0.1));
  }
}

@keyframes bar-glow {
  0%, 100% {
    box-shadow:
      0 0 0 rgba(96, 158, 175, 0),
      0 0 0 rgba(96, 158, 175, 0);
    filter: brightness(1) saturate(1);
  }
  25% {
    box-shadow:
      0 0 4px rgba(96, 158, 175, 0.3),
      0 -2px 8px rgba(96, 158, 175, 0.2);
    filter: brightness(1.1) saturate(1.2);
  }
  50% {
    box-shadow:
      0 0 8px rgba(96, 158, 175, 0.5),
      0 -4px 12px rgba(96, 158, 175, 0.3);
    filter: brightness(1.3) saturate(1.4);
  }
  75% {
    box-shadow:
      0 0 6px rgba(96, 158, 175, 0.4),
      0 -3px 10px rgba(96, 158, 175, 0.25);
    filter: brightness(1.15) saturate(1.3);
  }
}

/* Add subtle reflection effect */
.enhanced-wave::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30%;
  background: linear-gradient(to top, rgba(96, 158, 175, 0.1), transparent);
  border-radius: 0 0 12px 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-wave.playing::after {
  opacity: 1;
}

/* Seek Bar Styles */
.seek-bar-container {
  position: relative;
  background: linear-gradient(to right, #e5e7eb, #d1d5db);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.seek-bar-progress {
  position: relative;
  box-shadow: 0 0 8px rgba(96, 158, 175, 0.3);
}

.seek-bar-thumb {
  opacity: 0;
  transition: all 0.3s ease;
}

.seek-bar-container:hover .seek-bar-thumb {
  opacity: 1;
}

/* Video Processing Animations */
.video-processing-ring {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto;
}

.ring-1, .ring-2, .ring-3 {
  position: absolute;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: processing-spin 3s linear infinite;
}

.ring-1 {
  width: 120px;
  height: 120px;
  border-top-color: #609EAF;
  animation-duration: 2s;
}

.ring-2 {
  width: 90px;
  height: 90px;
  top: 15px;
  left: 15px;
  border-right-color: #4a7c8a;
  animation-duration: 3s;
  animation-direction: reverse;
}

.ring-3 {
  width: 60px;
  height: 60px;
  top: 30px;
  left: 30px;
  border-bottom-color: #609EAF;
  animation-duration: 1.5s;
}

/* Mobile responsive video processing */
@media (max-width: 640px) {
  .video-processing-ring {
    width: 80px;
    height: 80px;
  }

  .ring-1 {
    width: 80px;
    height: 80px;
    border-width: 2px;
  }

  .ring-2 {
    width: 60px;
    height: 60px;
    top: 10px;
    left: 10px;
    border-width: 2px;
  }

  .ring-3 {
    width: 40px;
    height: 40px;
    top: 20px;
    left: 20px;
    border-width: 2px;
  }
}

@keyframes processing-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Floating Particles for Video Processing */
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #609EAF;
  border-radius: 50%;
  animation: particle-float 6s infinite linear;
  opacity: 0.6;
}

.particle:nth-child(odd) {
  background: #4a7c8a;
}

.particle:nth-child(1) { left: 10%; animation-duration: 5s; }
.particle:nth-child(2) { left: 20%; animation-duration: 7s; }
.particle:nth-child(3) { left: 30%; animation-duration: 6s; }
.particle:nth-child(4) { left: 40%; animation-duration: 8s; }
.particle:nth-child(5) { left: 50%; animation-duration: 5.5s; }
.particle:nth-child(6) { left: 60%; animation-duration: 7.5s; }
.particle:nth-child(7) { left: 70%; animation-duration: 6.5s; }
.particle:nth-child(8) { left: 80%; animation-duration: 8.5s; }
.particle:nth-child(9) { left: 90%; animation-duration: 5.8s; }
.particle:nth-child(10) { left: 15%; animation-duration: 7.2s; }
.particle:nth-child(11) { left: 35%; animation-duration: 6.8s; }
.particle:nth-child(12) { left: 55%; animation-duration: 5.3s; }
.particle:nth-child(13) { left: 75%; animation-duration: 7.8s; }
.particle:nth-child(14) { left: 85%; animation-duration: 6.2s; }
.particle:nth-child(15) { left: 25%; animation-duration: 8.2s; }
.particle:nth-child(16) { left: 45%; animation-duration: 5.7s; }
.particle:nth-child(17) { left: 65%; animation-duration: 7.3s; }
.particle:nth-child(18) { left: 5%; animation-duration: 6.7s; }
.particle:nth-child(19) { left: 95%; animation-duration: 8.7s; }
.particle:nth-child(20) { left: 50%; animation-duration: 5.9s; }

@keyframes particle-float {
  0% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
    transform: scale(1);
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100px) scale(0);
    opacity: 0;
  }
}

/* Loading Spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spinner-spin 1s ease-in-out infinite;
}

@keyframes spinner-spin {
  to {
    transform: rotate(360deg);
  }
}

/* Button Disabled State */
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Responsive Adjustments */
@media (max-width: 640px) {
  .vinyl-player {
    width: 80px;
    height: 80px;
  }

  .vinyl-record {
    width: 80px;
    height: 80px;
  }

  .progress-ring {
    width: 100px;
    height: 100px;
    top: -10px;
    left: -10px;
  }

  .play-button-overlay {
    width: 40px;
    height: 40px;
  }

  .enhanced-wave {
    height: 60px;
    gap: 2px;
    padding: 8px 0;
  }

  .wave-bar {
    width: 4px;
    min-height: 6px;
  }
}
</style>
