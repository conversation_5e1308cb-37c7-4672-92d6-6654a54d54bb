{"name": "miyabimusic-backend", "version": "1.0.0", "main": "index.js", "dependencies": {"@aws-sdk/client-s3": "^3.696.0", "@aws-sdk/lib-storage": "^3.696.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.2", "replicate": "^1.0.1", "winston": "^3.11.0"}, "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "devDependencies": {"nodemon": "^3.1.7"}}