# meta/musicgen

## Generate music from a prompt or melody

![Status](https://img.shields.io/badge/Status-Cold-blue)
![Visibility](https://img.shields.io/badge/Visibility-Public-green)
![Runs](https://img.shields.io/badge/Runs-2.1M-orange)
![Hardware](https://img.shields.io/badge/Hardware-A100%20(80GB)-lightgrey)

**Links:** 
[GitHub](#) • [Paper](#) • [License](#)

## Run with an API

**Navigation:**  
[Playground](#) | [API](#) | [Examples](#) | [README](#) | [Versions](#)

### Input Methods
- Form
- JSON
- Node.js
- Python
- HTTP
- Cog
- Docker

## Quick Start with Node.js

### One-line Installation

```bash
npx create-replicate --model=meta/musicgen
```

### Manual Setup

#### 1. Install Replicate's Node.js client library
```bash
npm install replicate
```

#### 2. Set the API Token
```bash
export REPLICATE_API_TOKEN=****************************************
```

> ⚠️ **Security Note**: This is your Default API token. Keep it secure and never share it publicly.

#### 3. Initialize the Client
```javascript
import Replicate from "replicate";

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});
```

#### 4. Run the Model

The following example demonstrates how to use the model with its available parameters:

```javascript
const output = await replicate.run(
  "meta/musicgen:671ac645ce5e552cc63a54a2bbff63fcf798043055d2dac5fc9e36a837eedcfb",
  {
    input: {
      top_k: 250,
      top_p: 0,
      prompt: "Edo25 major g melodies that sound triumphant and cinematic. Leading up to a crescendo that resolves in a 9th harmonic",
      duration: 8,
      temperature: 1,
      continuation: false,
      model_version: "stereo-large",
      output_format: "mp3",
      continuation_start: 0,
      multi_band_diffusion: false,
      normalization_strategy: "peak",
      classifier_free_guidance: 3
    }
  }
);
console.log(output);
```

### Input Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| top_k | number | Number of samples to consider for generation (default: 250) |
| top_p | number | Nucleus sampling threshold (default: 0) |
| prompt | string | Text description of the desired music |
| duration | number | Length of the generated audio in seconds |
| temperature | number | Sampling temperature (default: 1) |
| continuation | boolean | Whether to continue from existing audio |
| model_version | string | Version of the model to use ("stereo-large" recommended) |
| output_format | string | Output audio format (default: "mp3") |
| continuation_start | number | Start time for continuation |
| multi_band_diffusion | boolean | Enable multi-band diffusion |
| normalization_strategy | string | Audio normalization strategy |
| classifier_free_guidance | number | Guidance scale for classifier-free generation |

For more detailed information, please refer to the [Node.js getting started guide](#).